// Cloudflare Workers version of dy-deno.ts with full functionality
// 抖音视频解析核心逻辑
const pattern = /"video":{"play_addr":{"uri":"([a-z0-9]+)"/;
const cVUrl = "https://www.iesdouyin.com/aweme/v1/play/?video_id=%s&ratio=1080p&line=0";
const statsRegex = /"statistics"\s*:\s*\{([\s\S]*?)\},/;
const regex = /"nickname":\s*"([^"]+)",\s*"signature":\s*"([^"]+)"/;
const ctRegex = /"create_time":\s*(\d+)/;
const descRegex = /"desc":\s*"([^"]+)"/;

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

async function doGet(url) {
  const headers = new Headers();
  headers.set(
    "User-Agent",
    "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36",
  );
  const resp = await fetch(url, { method: "GET", headers });
  return resp;
}

async function getVideoInfo(url) {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");

  const video_url = cVUrl.replace("%s", match[1]);
  const auMatch = body.match(regex);
  const ctMatch = body.match(ctRegex);
  const descMatch = body.match(descRegex);
  const statsMatch = body.match(statsRegex);

  if (statsMatch) {
    const innerContent = statsMatch[0];
    const awemeIdMatch = innerContent.match(/"aweme_id"\s*:\s*"([^"]+)"/);
    const commentCountMatch = innerContent.match(/"comment_count"\s*:\s*(\d+)/);
    const diggCountMatch = innerContent.match(/"digg_count"\s*:\s*(\d+)/);
    const shareCountMatch = innerContent.match(/"share_count"\s*:\s*(\d+)/);
    const collectCountMatch = innerContent.match(/"collect_count"\s*:\s*(\d+)/);

    const douyinVideoInfo = {
      aweme_id: awemeIdMatch ? awemeIdMatch[1] : null,
      comment_count: commentCountMatch ? parseInt(commentCountMatch[1]) : null,
      digg_count: diggCountMatch ? parseInt(diggCountMatch[1]) : null,
      share_count: shareCountMatch ? parseInt(shareCountMatch[1]) : null,
      collect_count: collectCountMatch ? parseInt(collectCountMatch[1]) : null,
      nickname: null,
      signature: null,
      desc: null,
      create_time: null,
      video_url: video_url,
      type: "video",
      image_url_list: null
    };

    if (auMatch) {
      douyinVideoInfo.nickname = auMatch[1];
      douyinVideoInfo.signature = auMatch[2];
    }
    if (ctMatch) {
      const date = new Date(parseInt(ctMatch[1]) * 1000);
      douyinVideoInfo.create_time = formatDate(date);
    }
    if (descMatch) {
      douyinVideoInfo.desc = descMatch[1];
    }

    return douyinVideoInfo;
  } else {
    throw new Error("No stats found in the response.");
  }
}

async function getVideoUrl(url) {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  return cVUrl.replace("%s", match[1]);
}

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url);

  // 设置CORS头
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type",
  };

  // 处理OPTIONS请求
  if (request.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // API路由处理
    if (url.searchParams.has("url")) {
      const inputUrl = url.searchParams.get("url");
      console.log("inputUrl:", inputUrl);

      // 返回完整json数据
      if (url.searchParams.has("data")) {
        const videoInfo = await getVideoInfo(inputUrl);
        return new Response(JSON.stringify(videoInfo), {
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders
          }
        });
      }

      // 返回视频下载链接
      const videoUrl = await getVideoUrl(inputUrl);
      return new Response(videoUrl, {
        headers: corsHeaders
      });
    }

    // 返回HTML页面
    const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音视频下载工具</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- jQuery CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 毛玻璃效果 */
        .glass {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        .glass-main {
            background: rgba(255, 255, 255, 0.95);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
        }
        
        /* 加载动画 */
        .spinner {
            border: 4px solid rgba(102, 126, 234, 0.2);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 按钮渐变效果 */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: linear-gradient(135deg, #e081e9 0%, #e3455a 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
        }
        
        /* Toast通知样式 */
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            font-size: 14px;
            max-width: 300px;
        }
        
        .toast.show {
            transform: translateX(0);
        }
        
        .toast.toast-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .toast.toast-error {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        }
        
        /* 信息卡片配色 */
        .info-card-basic {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.08) 0%, rgba(155, 89, 182, 0.08) 100%);
        }
        
        .info-card-author {
            background: linear-gradient(135deg, rgba(230, 126, 34, 0.08) 0%, rgba(231, 76, 60, 0.08) 100%);
        }
        
        .info-card-stats {
            background: linear-gradient(135deg, rgba(46, 204, 113, 0.08) 0%, rgba(26, 188, 156, 0.08) 100%);
        }
        
        /* 复制按钮样式 */
        .copy-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: linear-gradient(135deg, #218838 0%, #1ba085 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen p-5 font-sans">
    <div class="max-w-6xl mx-auto">
        <!-- 页面标题区域 -->
        <div class="text-center mb-8">
            <h1 class="text-5xl font-bold text-black mb-3 tracking-tight">🎵 抖音视频下载工具</h1>
            <p class="text-white text-xl mb-2 drop-shadow-lg">快速获取无水印视频链接和详细信息</p>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="glass glass-main rounded-3xl shadow-2xl p-8 mb-6 border border-white/20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-5 mb-6">
                <!-- 输入功能卡片 -->
                <div class="glass-card rounded-2xl shadow-lg border border-white/30 p-6 card-hover">
                    <div class="flex items-center gap-2 text-lg font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200">
                        <span class="text-xl">📝</span>
                        <span>视频链接输入</span>
                    </div>
                    <div class="mb-5">
                        <textarea
                            id="videoUrl"
                            class="w-full p-5 border-2 border-white/30 rounded-xl text-base transition-all duration-300 glass-card resize-none font-inherit leading-relaxed min-h-[120px] focus:outline-none focus:border-blue-500 focus:bg-white/95 focus:shadow-lg focus:ring-4 focus:ring-blue-500/15 focus:-translate-y-0.5"
                            placeholder="例如：https://v.douyin.com/xxxx/ 或复制分享的完整文本"
                            rows="3"
                        ></textarea>
                    </div>
                    <div class="flex gap-3 flex-wrap">
                        <button id="getVideoUrlBtn" class="flex-1 min-w-[140px] px-5 py-3 border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 flex items-center justify-center gap-2 btn-primary text-white shadow-lg">
                            获取视频链接
                        </button>
                        <button id="getVideoInfoBtn" class="flex-1 min-w-[140px] px-5 py-3 border-none rounded-lg text-sm font-medium cursor-pointer transition-all duration-200 flex items-center justify-center gap-2 btn-secondary text-white shadow-lg">
                            获取详细信息
                        </button>
                    </div>
                </div>
                
                <!-- 使用说明卡片 -->
                <div class="glass-card rounded-2xl shadow-lg border border-white/30 p-6 card-hover">
                    <div class="flex items-center gap-2 text-lg font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200">
                        <span class="text-xl">📖</span>
                        <span>使用说明</span>
                    </div>
                    <ul class="list-none p-0">
                        <li class="mb-3 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            支持抖音APP分享的短链接（如：https://v.douyin.com/xxxx/）
                        </li>
                        <li class="mb-3 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            可以直接粘贴抖音分享的完整文本，系统会自动提取链接
                        </li>
                        <li class="mb-3 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            "获取视频链接"返回可直接下载的无水印视频地址
                        </li>
                        <li class="mb-3 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            "获取详细信息"返回视频的完整元数据信息
                        </li>
                        <li class="mb-0 pl-6 relative text-gray-600 before:content-['✓'] before:absolute before:left-0 before:text-cyan-600 before:font-bold before:text-base">
                            点击复制按钮可快速复制结果到剪贴板
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 加载状态卡片 -->
            <div id="loadingCard" class="glass-card rounded-2xl shadow-lg border-2 border-blue-500/30 p-6 text-center hidden mb-5">
                <div class="flex items-center gap-2 text-lg font-semibold text-gray-700 mb-4 justify-center">
                    <span class="text-xl">⏳</span>
                    <span>正在处理</span>
                </div>
                <div class="spinner"></div>
                <p class="text-gray-600">正在解析视频信息，请稍候...</p>
            </div>
            
            <!-- 结果展示区域 -->
            <div id="resultsGrid" class="grid grid-cols-1 lg:grid-cols-3 gap-5 mt-6 hidden">
                <!-- 结果卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            console.log('卡片式布局页面已加载');

            // 提取URL的正则表达式
            const urlRegex = new RegExp('https?://v\\.douyin\\.com/[A-Za-z0-9]+/?');

            // 显示加载状态
            function showLoading() {
                $('#loadingCard').removeClass('hidden').addClass('block');
                $('#resultsGrid').removeClass('grid').addClass('hidden');
                $('button').prop('disabled', true);
            }

            // 隐藏加载状态
            function hideLoading() {
                $('#loadingCard').removeClass('block').addClass('hidden');
                $('button').prop('disabled', false);
            }

            // 显示Toast通知
            function showToast(message, type = 'success') {
                const icon = type === 'success' ? '✅' : '❌';
                const toast = $('<div class="toast toast-' + type + '">' + icon + ' ' + message + '</div>');

                $('body').append(toast);
                setTimeout(() => toast.addClass('show'), 100);
                setTimeout(() => {
                    toast.removeClass('show');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }

            // 复制到剪贴板
            function copyToClipboard(text, buttonElement) {
                // 如果text是编码的URL，先解码
                let actualText = text;
                try {
                    if (text.includes('%')) {
                        actualText = decodeURIComponent(text);
                    }
                } catch (e) {
                    // 如果解码失败，使用原始文本
                    actualText = text;
                }

                // 如果按钮有data-url属性，优先使用它
                const $btn = $(buttonElement);
                if ($btn.attr('data-url')) {
                    actualText = $btn.attr('data-url');
                }

                if (navigator.clipboard) {
                    navigator.clipboard.writeText(actualText).then(function() {
                        updateCopyButton(buttonElement);
                        showToast('链接已复制到剪贴板！');
                    }).catch(function(err) {
                        showToast('复制失败，请手动复制', 'error');
                    });
                } else {
                    // 降级方案
                    const textArea = $('<textarea>').val(actualText).appendTo('body');
                    textArea[0].select();
                    document.execCommand('copy');
                    textArea.remove();
                    updateCopyButton(buttonElement);
                    showToast('链接已复制到剪贴板！');
                }
            }

            // 更新复制按钮状态
            function updateCopyButton(buttonElement) {
                const $btn = $(buttonElement);
                const originalText = $btn.text();

                $btn.text('✅ 已复制').addClass('bg-green-500');
                setTimeout(() => {
                    $btn.text(originalText).removeClass('bg-green-500');
                }, 2000);
            }

            // 提取并验证URL
            function extractAndValidateUrl() {
                const input = $('#videoUrl').val().trim();
                if (!input) {
                    showToast('请输入抖音视频链接', 'error');
                    return null;
                }

                const match = input.match(urlRegex);
                if (match) {
                    return match[0];
                }

                if (input.startsWith('http')) {
                    return input;
                }

                showToast('未找到有效的抖音视频链接，请检查输入格式', 'error');
                return null;
            }

            // 显示结果卡片
            function showResults(cards) {
                const $resultsGrid = $('#resultsGrid');
                $resultsGrid.empty();

                cards.forEach(function(cardData) {
                    const $card = $('<div class="glass-card rounded-2xl shadow-lg border border-white/30 p-6 card-hover ' + (cardData.cardClass || '') + '"></div>');
                    const $cardTitle = $('<div class="flex items-center gap-2 text-lg font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200"></div>');
                    $cardTitle.html('<span class="text-xl">' + cardData.icon + '</span><span>' + cardData.title + '</span>');

                    const $cardContent = $('<div></div>').html(cardData.content);

                    $card.append($cardTitle).append($cardContent);
                    $resultsGrid.append($card);
                });

                $resultsGrid.removeClass('hidden').addClass('grid');
            }
            // 获取视频链接
            function getVideoUrl() {
                const url = extractAndValidateUrl();
                if (!url) return;

                showLoading();

                $.get('?url=' + encodeURIComponent(url))
                    .done(function(result) {
                        hideLoading();

                        // 安全地处理字符串，避免特殊字符导致的问题
                        function safeString(str) {
                            if (!str) return '';
                            return String(str).replace(/"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                        }

                        const cards = [{
                            icon: '📥',
                            title: '视频下载链接',
                            cardClass: 'info-card-basic',
                            content: '<div class="space-y-3">' +
                                '<div class="p-4 bg-gray-50 rounded-lg border">' +
                                    '<div class="text-sm text-gray-600 mb-2">下载链接：</div>' +
                                    '<div class="text-sm font-mono break-all text-gray-800 mb-3">' + safeString(result) + '</div>' +
                                    '<button onclick="copyToClipboard(\'' + encodeURIComponent(result) + '\', this)" class="copy-btn text-white px-4 py-2 rounded-lg text-sm font-medium border-none cursor-pointer" data-url="' + safeString(result) + '">复制链接</button>' +
                                '</div>' +
                                '<div class="text-xs text-gray-500 bg-yellow-50 p-3 rounded-lg border border-yellow-200">' +
                                    '💡 提示：点击复制按钮复制链接，然后在浏览器中打开即可下载视频' +
                                '</div>' +
                            '</div>'
                        }];

                        showResults(cards);
                        showToast('视频链接获取成功！');
                    })
                    .fail(function(xhr) {
                        hideLoading();
                        showToast('获取视频链接失败：' + (xhr.responseText || '网络错误'), 'error');
                    });
            }

            // 获取视频详细信息
            function getVideoInfo() {
                const url = extractAndValidateUrl();
                if (!url) return;

                showLoading();

                $.get('?data&url=' + encodeURIComponent(url))
                    .done(function(result) {
                        hideLoading();

                        try {
                            const videoInfo = JSON.parse(result);

                            // 安全地处理字符串，避免特殊字符导致的问题
                            function safeString(str) {
                                if (!str) return '';
                                return String(str).replace(/"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                            }

                            const cards = [
                                {
                                    icon: '📊',
                                    title: '基本信息',
                                    cardClass: 'info-card-basic',
                                    content: '<div class="space-y-3">' +
                                        '<div class="flex justify-between py-2 border-b border-gray-200"><span class="text-gray-600">视频ID：</span><span class="font-medium">' + safeString(videoInfo.aweme_id || '未知') + '</span></div>' +
                                        '<div class="flex justify-between py-2 border-b border-gray-200"><span class="text-gray-600">标题：</span><span class="font-medium">' + safeString(videoInfo.desc || '无标题') + '</span></div>' +
                                        '<div class="flex justify-between py-2"><span class="text-gray-600">创建时间：</span><span class="font-medium">' + safeString(videoInfo.create_time || '未知') + '</span></div>' +
                                    '</div>'
                                },
                                {
                                    icon: '👤',
                                    title: '作者信息',
                                    cardClass: 'info-card-author',
                                    content: '<div class="space-y-3">' +
                                        '<div class="flex justify-between py-2 border-b border-gray-200"><span class="text-gray-600">作者：</span><span class="font-medium">' + safeString(videoInfo.nickname || '未知') + '</span></div>' +
                                        '<div class="flex justify-between py-2"><span class="text-gray-600">签名：</span><span class="font-medium">' + safeString(videoInfo.signature || '无签名') + '</span></div>' +
                                    '</div>'
                                },
                                {
                                    icon: '📈',
                                    title: '统计数据',
                                    cardClass: 'info-card-stats',
                                    content: '<div class="space-y-3">' +
                                        '<div class="flex justify-between py-2 border-b border-gray-200"><span class="text-gray-600">点赞数：</span><span class="font-medium text-red-600">' + ((videoInfo.digg_count && videoInfo.digg_count.toLocaleString()) || '0') + '</span></div>' +
                                        '<div class="flex justify-between py-2 border-b border-gray-200"><span class="text-gray-600">评论数：</span><span class="font-medium text-blue-600">' + ((videoInfo.comment_count && videoInfo.comment_count.toLocaleString()) || '0') + '</span></div>' +
                                        '<div class="flex justify-between py-2 border-b border-gray-200"><span class="text-gray-600">分享数：</span><span class="font-medium text-green-600">' + ((videoInfo.share_count && videoInfo.share_count.toLocaleString()) || '0') + '</span></div>' +
                                        '<div class="flex justify-between py-2 border-b border-gray-200"><span class="text-gray-600">收藏数：</span><span class="font-medium text-purple-600">' + ((videoInfo.collect_count && videoInfo.collect_count.toLocaleString()) || '0') + '</span></div>' +
                                        '<div class="pt-3">' +
                                            '<div class="text-sm text-gray-600 mb-2">下载链接：</div>' +
                                            '<div class="text-xs font-mono break-all text-gray-800 mb-2 p-2 bg-gray-50 rounded">' + safeString(videoInfo.video_url || '获取失败') + '</div>' +
                                            (videoInfo.video_url ? '<button onclick="copyToClipboard(\'' + encodeURIComponent(videoInfo.video_url) + '\', this)" class="copy-btn text-white px-3 py-1 rounded text-xs font-medium border-none cursor-pointer" data-url="' + safeString(videoInfo.video_url) + '">复制链接</button>' : '') +
                                        '</div>' +
                                    '</div>'
                                }
                            ];

                            showResults(cards);
                            showToast('视频信息获取成功！');
                        } catch (parseError) {
                            showToast('响应格式错误：' + result, 'error');
                        }
                    })
                    .fail(function(xhr) {
                        hideLoading();
                        showToast('获取视频信息失败：' + (xhr.responseText || '网络错误'), 'error');
                    });
            }

            // 绑定事件
            $('#getVideoUrlBtn').click(getVideoUrl);
            $('#getVideoInfoBtn').click(getVideoInfo);

            // 回车键支持
            $('#videoUrl').keypress(function(e) {
                if (e.which === 13) {
                    getVideoUrl();
                }
            });

            // 自动聚焦输入框
            $('#videoUrl').focus();

            // 全局复制函数，供HTML中的onclick使用
            window.copyToClipboard = copyToClipboard;
        });
    </script>
</body>
</html>
    `;

    return new Response(htmlContent, {
      headers: {
        "content-type": "text/html; charset=utf-8",
        ...corsHeaders
      },
    });
  } catch (error) {
    console.error("处理请求时出错:", error);
    return new Response(`错误: ${error.message}`, {
      status: 500,
      headers: {
        "content-type": "text/plain; charset=utf-8",
        ...corsHeaders
      },
    });
  }
}
