// 抖音视频下载服务 - Cloudflare Workers 版本
const pattern = /"video":{"play_addr":{"uri":"([a-z0-9]+)"/;
const cVUrl = "https://www.iesdouyin.com/aweme/v1/play/?video_id=%s&ratio=1080p&line=0";
const statsRegex = /"statistics"\s*:\s*\{([\s\S]*?)\},/;
const regex = /"nickname":\s*"([^"]+)",\s*"signature":\s*"([^"]+)"/;
const ctRegex = /"create_time":\s*(\d+)/;
const descRegex = /"desc":\s*"([^"]+)"/;

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

async function doGet(url) {
  const headers = new Headers();
  headers.set(
    "User-Agent",
    "Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36",
  );
  const resp = await fetch(url, { method: "GET", headers });
  return resp;
}

async function getVideoInfo(url) {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  
  const video_url = cVUrl.replace("%s", match[1]);
  const auMatch = body.match(regex);
  const ctMatch = body.match(ctRegex);
  const descMatch = body.match(descRegex);
  const statsMatch = body.match(statsRegex);
  
  if (statsMatch) {
    const innerContent = statsMatch[0];
    const awemeIdMatch = innerContent.match(/"aweme_id"\s*:\s*"([^"]+)"/);
    const commentCountMatch = innerContent.match(/"comment_count"\s*:\s*(\d+)/);
    const diggCountMatch = innerContent.match(/"digg_count"\s*:\s*(\d+)/);
    const shareCountMatch = innerContent.match(/"share_count"\s*:\s*(\d+)/);
    const collectCountMatch = innerContent.match(/"collect_count"\s*:\s*(\d+)/);
    
    const douyinVideoInfo = {
      aweme_id: awemeIdMatch ? awemeIdMatch[1] : null,
      comment_count: commentCountMatch ? parseInt(commentCountMatch[1]) : null,
      digg_count: diggCountMatch ? parseInt(diggCountMatch[1]) : null,
      share_count: shareCountMatch ? parseInt(shareCountMatch[1]) : null,
      collect_count: collectCountMatch ? parseInt(collectCountMatch[1]) : null,
      nickname: null,
      signature: null,
      desc: null,
      create_time: null,
      video_url: video_url,
      type: "video",
      image_url_list: null
    };
    
    if (auMatch) {
      douyinVideoInfo.nickname = auMatch[1];
      douyinVideoInfo.signature = auMatch[2];
    }
    if (ctMatch) {
      const date = new Date(parseInt(ctMatch[1]) * 1000);
      douyinVideoInfo.create_time = formatDate(date);
    }
    if (descMatch) {
      douyinVideoInfo.desc = descMatch[1];
    }
    
    return douyinVideoInfo;
  } else {
    throw new Error("No stats found in the response.");
  }
}

async function getVideoUrl(url) {
  const resp = await doGet(url);
  const body = await resp.text();
  const match = pattern.exec(body);
  if (!match || !match[1]) throw new Error("Video ID not found in URL");
  return cVUrl.replace("%s", match[1]);
}

// 生成现代化的HTML页面
function generateHTML() {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音视频无水印下载工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px 30px;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .url-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .url-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            flex: 1;
            min-width: 150px;
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-section {
            margin-top: 30px;
            display: none;
        }

        .result-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .video-info {
            display: grid;
            gap: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e1e8ed;
        }

        .info-label {
            font-weight: 600;
            color: #666;
        }

        .info-value {
            color: #333;
            word-break: break-all;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }

        .copy-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 抖音视频下载工具</h1>
            <p>快速获取无水印视频链接和详细信息</p>
        </div>

        <div class="main-content">
            <div class="input-section">
                <div class="input-group">
                    <label for="videoUrl">请输入抖音视频分享链接：</label>
                    <input
                        type="text"
                        id="videoUrl"
                        class="url-input"
                        placeholder="例如：https://v.douyin.com/xxxx/ 或复制分享的完整文本"
                    >
                </div>

                <div class="button-group">
                    <button class="btn btn-primary" onclick="getVideoUrl()">
                        📥 获取视频链接
                    </button>
                    <button class="btn btn-secondary" onclick="getVideoInfo()">
                        📊 获取详细信息
                    </button>
                </div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在解析视频信息，请稍候...</p>
            </div>

            <div class="result-section" id="resultSection">
                <div class="result-card">
                    <div class="result-title" id="resultTitle">解析结果</div>
                    <div id="resultContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('JavaScript 已加载');

        // 提取URL的正则表达式
        const urlRegex = new RegExp('https?://v\\.douyin\\.com/[A-Za-z0-9]+/?');

        // 显示加载状态
        function showLoading() {
            console.log('显示加载状态');
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
        }

        // 隐藏加载状态
        function hideLoading() {
            console.log('隐藏加载状态');
            document.getElementById('loading').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
        }

        // 显示结果
        function showResult(title, content) {
            console.log('显示结果:', title);
            document.getElementById('resultTitle').textContent = title;
            document.getElementById('resultContent').innerHTML = content;
            document.getElementById('resultSection').style.display = 'block';
        }

        // 显示错误信息
        function showError(message) {
            console.log('显示错误:', message);
            const errorHtml = '<div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 10px; border-left: 5px solid #dc3545;">❌ ' + message + '</div>';
            showResult('解析失败', errorHtml);
        }

        // 显示成功信息
        function showSuccess(message) {
            console.log('显示成功:', message);
            const successHtml = '<div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 10px; border-left: 5px solid #28a745;">✅ ' + message + '</div>';
            showResult('解析成功', successHtml);
        }

        // 复制到剪贴板
        function copyToClipboard(text, buttonElement) {
            console.log('复制到剪贴板:', text);
            try {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(function() {
                        // 按钮反馈
                        if (buttonElement) {
                            const originalText = buttonElement.textContent;
                            const originalBg = buttonElement.style.backgroundColor;
                            buttonElement.textContent = '✅ 已复制';
                            buttonElement.style.backgroundColor = '#28a745';

                            setTimeout(() => {
                                buttonElement.textContent = originalText;
                                buttonElement.style.backgroundColor = originalBg;
                            }, 2000);
                        }
                    }).catch(function(err) {
                        console.error('复制失败:', err);
                    });
                } else {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    // 按钮反馈
                    if (buttonElement) {
                        const originalText = buttonElement.textContent;
                        const originalBg = buttonElement.style.backgroundColor;
                        buttonElement.textContent = '✅ 已复制';
                        buttonElement.style.backgroundColor = '#28a745';

                        setTimeout(() => {
                            buttonElement.textContent = originalText;
                            buttonElement.style.backgroundColor = originalBg;
                        }, 2000);
                    }
                }
            } catch (err) {
                console.error('复制失败:', err);
            }
        }

        // 提取并验证URL
        function extractAndValidateUrl() {
            console.log('提取并验证URL');
            const input = document.getElementById('videoUrl').value.trim();
            console.log('输入内容:', input);

            if (!input) {
                showError('请输入抖音视频链接');
                return null;
            }

            // 尝试从输入文本中提取URL
            const match = input.match(urlRegex);
            if (match) {
                console.log('找到匹配的URL:', match[0]);
                return match[0];
            }

            // 如果直接是URL格式
            if (input.startsWith('http')) {
                console.log('直接URL格式:', input);
                return input;
            }

            showError('未找到有效的抖音视频链接，请检查输入格式');
            return null;
        }

        // 获取视频链接
        function getVideoUrl() {
            console.log('getVideoUrl 函数被调用');
            const url = extractAndValidateUrl();
            if (!url) return;

            showLoading();

            fetch('?url=' + encodeURIComponent(url))
                .then(function(response) {
                    console.log('收到响应:', response.status);
                    return response.text();
                })
                .then(function(result) {
                    console.log('响应内容:', result);
                    hideLoading();

                    const copyButton = '<button class="copy-btn" onclick="copyToClipboard(\'' + result + '\', this)">复制链接</button>';
                    const content =
                        '<div class="info-item">' +
                            '<span class="info-label">视频下载链接：</span>' +
                            '<span class="info-value">' + result + ' ' + copyButton + '</span>' +
                        '</div>' +
                        '<div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 14px;">' +
                            '💡 提示：点击复制按钮复制链接，然后在浏览器中打开即可下载视频' +
                        '</div>';
                    showResult('视频链接获取成功', content);
                })
                .catch(function(error) {
                    console.error('请求失败:', error);
                    hideLoading();
                    showError('网络请求失败，请检查网络连接: ' + error.message);
                });
        }

        // 获取视频详细信息
        function getVideoInfo() {
            console.log('getVideoInfo 函数被调用');
            const url = extractAndValidateUrl();
            if (!url) return;

            showLoading();

            fetch('?data&url=' + encodeURIComponent(url))
                .then(function(response) {
                    console.log('收到详细信息响应:', response.status);
                    return response.text();
                })
                .then(function(result) {
                    console.log('详细信息响应内容:', result);
                    hideLoading();

                    try {
                        const videoInfo = JSON.parse(result);
                        const videoUrl = videoInfo.video_url || '';
                        const copyButton = '<button class="copy-btn" onclick="copyToClipboard(\'' + videoUrl + '\', this)">复制链接</button>';

                        const content =
                            '<div class="video-info">' +
                                '<div class="info-item">' +
                                    '<span class="info-label">视频ID：</span>' +
                                    '<span class="info-value">' + (videoInfo.aweme_id || '未知') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">标题：</span>' +
                                    '<span class="info-value">' + (videoInfo.desc || '无标题') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">作者：</span>' +
                                    '<span class="info-value">' + (videoInfo.nickname || '未知') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">作者签名：</span>' +
                                    '<span class="info-value">' + (videoInfo.signature || '无签名') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">创建时间：</span>' +
                                    '<span class="info-value">' + (videoInfo.create_time || '未知') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">点赞数：</span>' +
                                    '<span class="info-value">' + ((videoInfo.digg_count && videoInfo.digg_count.toLocaleString()) || '0') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">评论数：</span>' +
                                    '<span class="info-value">' + ((videoInfo.comment_count && videoInfo.comment_count.toLocaleString()) || '0') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">分享数：</span>' +
                                    '<span class="info-value">' + ((videoInfo.share_count && videoInfo.share_count.toLocaleString()) || '0') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">收藏数：</span>' +
                                    '<span class="info-value">' + ((videoInfo.collect_count && videoInfo.collect_count.toLocaleString()) || '0') + '</span>' +
                                '</div>' +
                                '<div class="info-item">' +
                                    '<span class="info-label">下载链接：</span>' +
                                    '<span class="info-value">' + (videoInfo.video_url || '获取失败') + ' ' + (videoInfo.video_url ? copyButton : '') + '</span>' +
                                '</div>' +
                            '</div>';
                        showResult('视频详细信息', content);
                    } catch (parseError) {
                        console.error('JSON解析失败:', parseError);
                        showError('响应格式错误: ' + result);
                    }
                })
                .catch(function(error) {
                    console.error('详细信息请求失败:', error);
                    hideLoading();
                    showError('解析失败，请检查链接是否正确: ' + error.message);
                });
        }

        // 将函数设为全局，供onclick使用
        window.getVideoUrl = getVideoUrl;
        window.getVideoInfo = getVideoInfo;
        window.copyToClipboard = copyToClipboard;

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');

            // 回车键支持
            const urlInput = document.getElementById('videoUrl');
            if (urlInput) {
                urlInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        getVideoUrl();
                    }
                });

                // 自动聚焦输入框
                urlInput.focus();
            }
        });
    </script>
</body>
</html>
  `;
}

// Cloudflare Workers 事件监听器
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  console.log("Method:", request.method);
  const url = new URL(request.url);

  // 设置CORS头
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type",
  };

  // 处理OPTIONS请求
  if (request.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (url.searchParams.has("url")) {
      const inputUrl = url.searchParams.get("url");
      console.log("inputUrl:", inputUrl);

      // 返回完整json数据
      if (url.searchParams.has("data")) {
        const videoInfo = await getVideoInfo(inputUrl);
        return new Response(JSON.stringify(videoInfo), {
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders
          }
        });
      }

      // 返回视频下载链接
      const videoUrl = await getVideoUrl(inputUrl);
      return new Response(videoUrl, {
        headers: corsHeaders
      });
    } else {
      // 返回HTML页面
      return new Response(generateHTML(), {
        headers: {
          "Content-Type": "text/html; charset=utf-8",
          ...corsHeaders
        }
      });
    }
  } catch (error) {
    console.error("处理请求时出错:", error);
    return new Response(`错误: ${error.message}`, {
      status: 500,
      headers: corsHeaders
    });
  }
}
